{{ 'quiz-result.css' | asset_url | stylesheet_tag: media: 'screen' }}

<div class="quiz-result-banner {{ section.settings.custom_class }}">
  <div class="container">
    <div class="quiz-result-inner">
      <div class="quiz-result-heading">
        {% if section.settings.title != blank %}
          <div class="quiz-result-title">
            <h2>{{ section.settings.title }}</h2>
          </div>
        {% endif %}
        <div class="quiz-result-subtitle">
          <span class="subtitle-quantity">1 items</span>
          <span class="subtitle-subtotal">$458</span>
          <span class="subtitle-total">$249</span>
        </div>
      </div>
      <div class="quiz-result-wrapper">
        <div class="quiz-result-product">
          <div class="quiz-result-product-list">
            <div class="result-product">
              <div class="result-product-wrap">
                <div class="result-product-image">
                  <img src="https://upstep-custom-orthotics.myshopify.com/cdn/shop/files/d774ad378abc8c46687b93c6.webp">
                </div>
                <div class="result-product-info">
                  <div class="result-product-name">
                    <h3>
                      <a href="https://upstep-custom-orthotics.myshopify.com/products/on-my-feet-all-day-custom-orthotics">
                        On My Feet All Day – Custom Orthotics
                      </a>
                    </h3>
                  </div>
                  <div class="result-product-price">
                    <span class="product-old-price">$458</span>
                    <span class="product-new-price">SALE $249</span>
                  </div>
                  <div class="result-product-quantity">
                    <div class="result-product-remove">Remove</div>
                    <div class="product-quantity-count">
                      <button type="button" class="quantity-btn_plus">
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                          <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                        </svg>
                      </button>
                      <div class="quantity-value_text"><span class="product-quantity">1</span> <span>Pair</span></div>
                      <button type="button" class="quantity-btn_minus">
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                          <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
                <div class="result-product-content">
                  <h4 class="product-content-title accordion-toggle">Your Upstep Info</h4>
                  <div class="product-content-list accordion-content">
                    <p><strong>What shoe size do you wear?:</strong> 7</p>
                    <p><strong>Pains:</strong> Right and Left The arch</p>
                    <p><strong>Diagnosis:</strong> Right and Left Plantar Fasciitis</p>
                    <p><strong>Sport:</strong> Basketball</p>
                    <p><strong>Basketball Shoes:</strong> Low/Mid Top</p>
                    <p><strong>Gender:</strong> Man</p>
                  </div>
                </div>
              </div>
              <div class="result-product-removing_message">
                <div class="result-product-message_wrapper">
                  <div class="result-product-form_block">
                    <div class="result-product-message">Remove from cart?</div>
                    <div class="result-product-buttons_wrapper">
                      <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
                      <button type="button" class="btn btn_gray no-remove-product">No</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="result-suggest-product">
            {% if section.settings.suggest_title != blank %}
              <div class="result-suggest-title">
                <h3>{{ section.settings.suggest_title }}</h3>
              </div>
            {% endif %}
            <div class="result-suggest-list">
              {% for product in section.settings.product_list %}
                <div class="suggest-product-block">
                  <div class="suggest-product-image">
                    <img src="{{ product.featured_image | img_url: '300x' }}" alt="{{ product.title }}">
                  </div>
                  <div class="suggest-product-info">
                    <div class="suggest-product-content">
                      <div class="suggest-product-title">
                        <h5>{{ product.title }}</h5>
                      </div>
                      <div class="suggest-product-price">
                        <span class="suggest-product-price-new">
                          {{ product.price | money }}
                        </span>
                        {% if product.compare_at_price > product.price %}
                          <span class="suggest-product-price-old">
                            {{ product.compare_at_price | money }}
                          </span>
                        {% endif %}
                      </div>
                    </div>
                    <div class="suggest-product-add">
                      <button class="suggest-product-add-btn" type="button" data-suggest-pro-id="{{ product.id }}" data-suggest-pro-var-id="{{ product.variants.first.id }}" data-suggest-pro-price="{{ product.price }}" data-suggest-pro-compare-price="{{ product.compare_at_price }}">Add</button>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
        <div class="quiz-result-cart">
          <div class="quiz-result-cart-title">Summary</div>
          <div class="quiz-result-cart-info">
            <div class="result-shipping">
              <div class="result-shipping_img">
                <img
                  src="https://dcpsvjmu5hpqr.cloudfront.net/images/2021-11/638755d51c7bb99069d28f86.svg"
                  alt="Free &amp; fast shipping.svg"
                >
              </div>
              <div class="result-shipping_title">Free Shipping</div>
            </div>
            <div class="result-cart-top">
              <div class="result-cart-subtotal result-cart-text">
                <div class="cart-subtotal-label">Subtotal</div>
                <div class="cart-subtotal-price">$458</div>
              </div>
              <div class="result-cart-shipping result-cart-text">
                <div class="cart-shipping-label">Shipping</div>
                <div class="cart-shipping-price">FREE</div>
              </div>
              <div class="result-cart-discounts result-cart-text">
                <div class="cart-discounts-label">Discounts</div>
                <div class="cart-discounts-price">- $209</div>
              </div>
            </div>
            <div class="result-cart-bottom">
              <div class="result-cart-total">
                <div class="cart-discounts-label">Total:</div>
                <div class="cart-discounts-price">$249</div>
              </div>
              <div class="result-cart-submit">
                <button class="overlay-buy_button result-cart-btn" type="submit">Secure Checkout</button>
              </div>
              <ul class="result-cart-payment">
                {%- for payment_method in shop.enabled_payment_types -%}
                  <li>{{ payment_method | payment_type_svg_tag }}</li>
                {%- endfor -%}
              </ul>
              <div class="result-cart-free-shipping">
                <svg xmlns="http://www.w3.org/2000/svg" width="11" height="12" fill="none">
                  <path stroke="#53BA65" stroke-linecap="round" stroke-width="2" d="m10 2-5.143 8M1 5.2 4.857 10"/>
                </svg>
                <p>Your order is eligible for free express shipping</p>
              </div>
            </div>
          </div>
          <ul class="result-cart-payment-mobile">
            {%- for payment_method in shop.enabled_payment_types -%}
              <li>{{ payment_method | payment_type_svg_tag }}</li>
            {%- endfor -%}
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div class="quiz-result-fixed_button">
    <div class="quiz-result-button_wrapper">
      <div class="quiz-result-total_row">
        <div class="result-fixed-total-label">Total</div>
        <div class="result-fixed-total_price">
          <div class="result-fixed-price_old">$458</div>
          <div class="result-fixed-price_new">$249</div>
        </div>
      </div>
      <button class="result-fixed-btn" type="button">Secure Checkout</button>
      <div class="result-fixed-bottom-line">180-day money-back guarantee</div>
    </div>
  </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script>
$(document).ready(function () {
  // Load existing cart data on page load
  loadExistingCartData();

  // Check for cart changes every 5 seconds
  setInterval(function() {
    checkCartSync();
  }, 5000);

  function initAccordion() {
    if ($(window).width() <= 767) {
      $(document).on("click", ".accordion-toggle", function () {
          $(this).toggleClass("active");
          $(this).next(".accordion-content").slideToggle();
        });
    } else {
      $(".accordion-content").show();
      $(".accordion-toggle").removeClass("active");
    }
  }

  initAccordion();
  $(window).resize(function () {
    initAccordion();
  });

  $(document).on("click", ".quantity-btn_plus", function () {
    let $qty = $(this)
      .siblings(".quantity-value_text")
      .find(".product-quantity");
    let currentValue = parseInt($qty.text());
    $qty.text(currentValue + 1);
    updateCartSummary();
    syncToShopifyCart();
  });

  $(document).on("click", ".quantity-btn_minus", function () {
    let $qty = $(this)
      .siblings(".quantity-value_text")
      .find(".product-quantity");
    let currentValue = parseInt($qty.text());

    if (currentValue > 1) {
      $qty.text(currentValue - 1);
      updateCartSummary();
      syncToShopifyCart();
    } else {
      let $product = $(this).closest(".result-product");

      if ($(window).width() <= 767) {
        $product.find(".result-product-removing_message")
                .addClass("show-remove-message")
                .fadeIn(200);
      } else {
        return;
      }
    }
  });

  $(document).on("click", ".result-product-remove", function () {
    let $product = $(this).closest(".result-product");
    $product.find(".result-product-removing_message").addClass("show-remove-message").fadeIn(200);
  });

  $(document).on("click", ".result-product-removing_message .no-remove-product", function () {
    let $product = $(this).closest(".result-product");
    $product.find(".result-product-removing_message").removeClass("show-remove-message").fadeOut(200);
  });

  $(document).on("click", ".result-product-removing_message .yes-remove-product", function () {
    let $product = $(this).closest(".result-product");
    $product.remove();
    updateCartSummary();
    syncToShopifyCart();
  });
});

let urlParams = new URLSearchParams(window.location.search);
let code = urlParams.get("code");
if (code) {
  $.ajax({
    url: "https://api.quizell.com/api/leadData",
    type: "POST",
    contentType: "application/json",
    data: JSON.stringify({
      code: code,
      question_answers: true,
      product_fields: [
        "id",
        "title",
        "external_id",
        "image",
        "price",
        "compare_at_price",
        "detail_link",
        "variant_id",
      ],
    }),
    success: function (response) {
      if (response.data && response.status == "success") {
        var product_details = response.data.products[0];

        var pro_title = product_details.title;
        var pro_img = product_details.image;
        var actual_price = product_details.price * 100;
        var actual_compare_price = product_details.compare_at_price * 100;
        var pro_price = "SALE " + USFormatMoney(actual_price);
        var pro_compare_price = USFormatMoney(actual_compare_price);
        var pro_link = product_details.detail_link;

        var pains = response.data.questionAnswers[1].selectedOption;
        var diagnosis = response.data.questionAnswers[2].selectedOption.value;
        var upstep_plan = response.data.questionAnswers[3].selectedOption.value;
        var sport = response.data.questionAnswers[4].selectedOption.value;
        var sport_preffer = response.data.questionAnswers[5].selectedOption.value;
        var gender = response.data.questionAnswers[6].selectedOption.value;
        var shoe_size = response.data.questionAnswers[7].selectedOption.value;

        let titles = [];
        pains.forEach(function (pain, index) {
          titles.push(pain.value.trim());
        });

        let pain_str = titles.join(", ");

        var pro_html = `<div class="result-product" data-pro-id="${product_details.external_id}" data-var-id="${product_details.variant_id}" data-price="${actual_price}" data-compare-price="${actual_compare_price}">
                          <div class="result-product-wrap">
                            <div class="result-product-image">
                              <img src="${pro_img}">
                            </div>
                            <div class="result-product-info">
                              <div class="result-product-name">
                                <h3>
                                  <a href="${pro_link}">
                                    ${pro_title}
                                  </a>
                                </h3>
                              </div>
                              <div class="result-product-price">
                                <span class="product-old-price">${pro_compare_price}</span>
                                <span class="product-new-price">${pro_price}</span>
                              </div>
                              <div class="result-product-quantity">
                                <div class="result-product-remove">Remove</div>
                                <div class="product-quantity-count">
                                  <button type="button" class="quantity-btn_plus">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                                      <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                                    </svg>
                                  </button>
                                  <div class="quantity-value_text"><span class="product-quantity">1</span> <span>Pair</span></div>
                                  <button type="button" class="quantity-btn_minus">
                                     <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                                      <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                                    </svg>
                                  </button>
                                </div>
                              </div>
                            </div>
                            <div class="result-product-content">
                              <h4 class="product-content-title accordion-toggle">Your Upstep Info</h4>
                              <div class="product-content-list accordion-content">
                                <p><strong>What shoe size do you wear?:</strong> ${shoe_size}</p>
                                <p><strong>Pains:</strong> ${pain_str}</p>
                                <p><strong>Diagnosis:</strong> ${diagnosis}</p>
                                <p><strong>${upstep_plan}:</strong> ${sport}</p>
                                <p><strong>${sport} Shoes:</strong> ${sport_preffer}</p>
                                <p><strong>Gender:</strong> ${gender}</p>
                              </div>
                            </div>
                          </div>
                          <div class="result-product-removing_message">
                            <div class="result-product-message_wrapper">
                              <div class="result-product-form_block">
                                <div class="result-product-message">Remove from cart?</div>
                                <div class="result-product-buttons_wrapper">
                                  <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
                                  <button type="button" class="btn btn_gray no-remove-product">No</button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>`;
        $(".quiz-result-product-list").html(pro_html);
        updateCartSummary();
        // Sync the quiz result to Shopify cart immediately
        syncToShopifyCart();
      }
    },
  });
} else {
  // If no quiz code, check if there are items in cart and display them
  loadExistingCartData();
}

$(document).on("click", ".suggest-product-add-btn", function () {
  let $btn = $(this);
  let pro_id = $btn.data("suggest-pro-id");
  let var_id = $btn.data("suggest-pro-var-id");
  let pro_price = $btn.data("suggest-pro-price");
  let pro_compare_price = $btn.data("suggest-pro-compare-price");

  let pro_title = $btn.closest(".suggest-product-block").find("h5").text();
  let pro_img = $btn.closest(".suggest-product-block").find("img").attr("src");

  let pro_price_formatted = "SALE " + USFormatMoney(pro_price);
  let pro_compare_price_formatted = USFormatMoney(pro_compare_price);

  let pro_html = `
    <div class="result-product" 
         data-pro-id="${pro_id}" 
         data-var-id="${var_id}" 
         data-price="${pro_price}" 
         data-compare-price="${pro_compare_price}">
      <div class="result-product-wrap">
        <div class="result-product-image">
          <img src="${pro_img}">
        </div>
        <div class="result-product-info">
          <div class="result-product-name">
            <h3>${pro_title}</h3>
          </div>
          <div class="result-product-price">
            <span class="product-old-price">${pro_compare_price_formatted}</span>
            <span class="product-new-price">${pro_price_formatted}</span>
          </div>
          <div class="result-product-quantity">
            <div class="result-product-remove">Remove</div>
            <div class="product-quantity-count">
              <button type="button" class="quantity-btn_plus">
                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                  <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                </svg>
              </button>
              <div class="quantity-value_text">
                <span class="product-quantity">1</span> <span>Pair</span>
              </div>
              <button type="button" class="quantity-btn_minus">
                 <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                    <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                  </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="result-product-removing_message">
        <div class="result-product-message_wrapper">
          <div class="result-product-form_block">
            <div class="result-product-message">Remove from cart?</div>
            <div class="result-product-buttons_wrapper">
              <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
              <button type="button" class="btn btn_gray no-remove-product">No</button>
            </div>
          </div>
        </div>
      </div>
    </div>`;

  $(".quiz-result-product-list").append(pro_html);
  updateCartSummary();
  syncToShopifyCart();
});

function updateCartSummary() {
  let totalQty = $(".result-product-wrap").length;
  let subtotal = 0;
  let total = 0;

  $(".result-product").each(function () {
    let qty = parseInt($(this).find(".product-quantity").text());
    let oldPrice = parseFloat(
      $(this).find(".product-old-price").text().replace(/[^0-9.-]+/g, "")
    );
    let newPrice = parseFloat(
      $(this).find(".product-new-price").text().replace(/[^0-9.-]+/g, "")
    );

    if (!isNaN(oldPrice)) subtotal += oldPrice * qty;
    if (!isNaN(newPrice)) total += newPrice * qty;
  });

  let discount = subtotal - total;
  let subtotalCents = Math.round(subtotal * 100);
  let totalCents = Math.round(total * 100);
  let discountCents = Math.round(discount * 100);

  $(".subtitle-quantity").text(totalQty + " items");
  $(".subtitle-subtotal").text(USFormatMoney(subtotalCents));
  $(".subtitle-total").text(USFormatMoney(totalCents));

  $(".cart-subtotal-price").text(USFormatMoney(subtotalCents));
  $(".result-cart-discounts .cart-discounts-price").text("- " + USFormatMoney(discountCents));
  $(".result-cart-total .cart-discounts-price").text(USFormatMoney(totalCents));

  $(".result-fixed-price_old").text(USFormatMoney(subtotalCents));
  $(".result-fixed-price_new").text(USFormatMoney(totalCents));

  // Save current state to localStorage
  saveResultPageState();
}

// Function to save result page state to localStorage
function saveResultPageState() {
  let resultState = [];

  $(".result-product").each(function () {
    let $product = $(this);
    let productData = {
      varId: $product.data("var-id"),
      proId: $product.data("pro-id"),
      price: $product.data("price"),
      comparePrice: $product.data("compare-price"),
      quantity: parseInt($product.find(".product-quantity").text()),
      title: $product.find(".result-product-name h3 a").text(),
      image: $product.find(".result-product-image img").attr("src"),
      url: $product.find(".result-product-name h3 a").attr("href")
    };
    resultState.push(productData);
  });

  localStorage.setItem('quizResultState', JSON.stringify(resultState));
}

// Function to load result page state from localStorage
function loadResultPageState() {
  let savedState = localStorage.getItem('quizResultState');
  if (!savedState) return false;

  try {
    let resultState = JSON.parse(savedState);
    if (!resultState || resultState.length === 0) return false;

    let productsHtml = '';

    resultState.forEach(function(item) {
      let pro_price_formatted = "SALE " + USFormatMoney(item.price);
      let pro_compare_price_formatted = USFormatMoney(item.comparePrice);

      productsHtml += `
        <div class="result-product"
             data-pro-id="${item.proId}"
             data-var-id="${item.varId}"
             data-price="${item.price}"
             data-compare-price="${item.comparePrice}">
          <div class="result-product-wrap">
            <div class="result-product-image">
              <img src="${item.image}">
            </div>
            <div class="result-product-info">
              <div class="result-product-name">
                <h3>
                  <a href="${item.url}">
                    ${item.title}
                  </a>
                </h3>
              </div>
              <div class="result-product-price">
                <span class="product-old-price">${pro_compare_price_formatted}</span>
                <span class="product-new-price">${pro_price_formatted}</span>
              </div>
              <div class="result-product-quantity">
                <div class="result-product-remove">Remove</div>
                <div class="product-quantity-count">
                  <button type="button" class="quantity-btn_plus">
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                      <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                    </svg>
                  </button>
                  <div class="quantity-value_text">
                    <span class="product-quantity">${item.quantity}</span> <span>Pair</span>
                  </div>
                  <button type="button" class="quantity-btn_minus">
                     <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                        <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                      </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="result-product-removing_message">
            <div class="result-product-message_wrapper">
              <div class="result-product-form_block">
                <div class="result-product-message">Remove from cart?</div>
                <div class="result-product-buttons_wrapper">
                  <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
                  <button type="button" class="btn btn_gray no-remove-product">No</button>
                </div>
              </div>
            </div>
          </div>
        </div>`;
    });

    $(".quiz-result-product-list").html(productsHtml);
    updateCartSummary();
    return true;

  } catch (e) {
    console.error("Error loading saved state:", e);
    return false;
  }
}

// Function to load existing cart data and sync with result page
function loadExistingCartData() {
  $.ajax({
    type: "GET",
    url: "/cart.js",
    dataType: "json",
    success: function (cart) {
      syncCartToResultPage(cart);
    },
    error: function (err) {
      console.error("Error loading cart:", err);
    }
  });
}

// Function to sync Shopify cart to result page
function syncCartToResultPage(cart) {
  if (!cart || !cart.items || cart.items.length === 0) {
    return;
  }

  // If result page is empty, populate it with cart items
  if ($(".result-product").length === 0) {
    displayCartItemsOnResultPage(cart);
    return;
  }

  // Update quantities based on cart data
  $(".result-product").each(function () {
    let $product = $(this);
    let varId = $product.data("var-id");

    // Find matching item in cart
    let cartItem = cart.items.find(item => item.variant_id == varId);

    if (cartItem) {
      // Update quantity to match cart
      $product.find(".product-quantity").text(cartItem.quantity);
    }
  });

  updateCartSummary();
}

// Function to display cart items on result page when no quiz data
function displayCartItemsOnResultPage(cart) {
  if (!cart || !cart.items || cart.items.length === 0) {
    return;
  }

  let productsHtml = '';

  cart.items.forEach(function(item) {
    let pro_price_formatted = "SALE " + USFormatMoney(item.price);
    let pro_compare_price_formatted = USFormatMoney(item.original_price || item.price);

    productsHtml += `
      <div class="result-product"
           data-pro-id="${item.product_id}"
           data-var-id="${item.variant_id}"
           data-price="${item.price}"
           data-compare-price="${item.original_price || item.price}">
        <div class="result-product-wrap">
          <div class="result-product-image">
            <img src="${item.image || ''}">
          </div>
          <div class="result-product-info">
            <div class="result-product-name">
              <h3>
                <a href="${item.url}">
                  ${item.product_title}
                </a>
              </h3>
            </div>
            <div class="result-product-price">
              <span class="product-old-price">${pro_compare_price_formatted}</span>
              <span class="product-new-price">${pro_price_formatted}</span>
            </div>
            <div class="result-product-quantity">
              <div class="result-product-remove">Remove</div>
              <div class="product-quantity-count">
                <button type="button" class="quantity-btn_plus">
                  <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                    <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M5.709 9.2a.75.75 0 1 0 0 1.5zm4.948 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.949 1.5a.75.75 0 0 0 0-1.5zm-4.199-.75a.75.75 0 0 0-1.5 0zm-1.5 4.947a.75.75 0 1 0 1.5 0zm0-4.949a.75.75 0 1 0 1.5 0zM11.407 5a.75.75 0 0 0-1.5 0zM5.71 10.7h4.948V9.2H5.709zm4.949 0h4.948V9.2h-4.948zm-.75-.75v4.947h1.5V9.95zm1.5-.002V5h-1.5v4.948z"/>
                  </svg>
                </button>
                <div class="quantity-value_text">
                  <span class="product-quantity">${item.quantity}</span> <span>Pair</span>
                </div>
                <button type="button" class="quantity-btn_minus">
                   <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" fill="none">
                      <circle cx="10.5" cy="10.5" r="10.5" fill="#F9F9F9"/><path fill="#A0A0A0" d="M6 10.2a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm0-1.5a.75.75 0 0 0 0 1.5zm4.95 1.5a.75.75 0 0 0 0-1.5zm-9.9 0h4.95v-1.5H6zm4.95 0h4.95v-1.5h-4.95z"/>
                    </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="result-product-removing_message">
          <div class="result-product-message_wrapper">
            <div class="result-product-form_block">
              <div class="result-product-message">Remove from cart?</div>
              <div class="result-product-buttons_wrapper">
                <button type="button" class="btn btn_light_blue yes-remove-product">Yes</button>
                <button type="button" class="btn btn_gray no-remove-product">No</button>
              </div>
            </div>
          </div>
        </div>
      </div>`;
  });

  $(".quiz-result-product-list").html(productsHtml);
  updateCartSummary();
}

// Function to check if cart is in sync
function checkCartSync() {
  $.ajax({
    type: "GET",
    url: "/cart.js",
    dataType: "json",
    success: function (cart) {
      let isInSync = true;
      let resultProducts = {};

      // Get current result page state
      $(".result-product").each(function () {
        let varId = $(this).data("var-id");
        let qty = parseInt($(this).find(".product-quantity").text());
        if (varId && qty > 0) {
          resultProducts[varId] = qty;
        }
      });

      // Check if cart matches result page
      let cartProducts = {};
      if (cart && cart.items) {
        cart.items.forEach(function(item) {
          cartProducts[item.variant_id] = item.quantity;
        });
      }

      // Compare the two states
      for (let varId in resultProducts) {
        if (!cartProducts[varId] || cartProducts[varId] !== resultProducts[varId]) {
          isInSync = false;
          break;
        }
      }

      for (let varId in cartProducts) {
        if (!resultProducts[varId] || resultProducts[varId] !== cartProducts[varId]) {
          isInSync = false;
          break;
        }
      }

      // If not in sync, sync the cart to match result page
      if (!isInSync) {
        syncCartToResultPage(cart);
      }
    },
    error: function (err) {
      console.error("Error checking cart sync:", err);
    }
  });
}

// Function to sync result page to Shopify cart
function syncToShopifyCart() {
  // Clear cart first, then add all items with current quantities
  $.ajax({
    type: "POST",
    url: "/cart/clear.js",
    dataType: "json",
    success: function () {
      let items = [];

      $(".result-product").each(function () {
        let varId = $(this).data("var-id");
        let qty = parseInt($(this).find(".product-quantity").text());

        if (varId && qty > 0) {
          items.push({
            id: varId,
            quantity: qty
          });
        }
      });

      if (items.length > 0) {
        $.ajax({
          type: "POST",
          url: "/cart/add.js",
          data: JSON.stringify({ items: items }),
          dataType: "json",
          contentType: "application/json",
          success: function (cart) {
            // Update cart drawer/counter if needed
            if (typeof ajaxCart !== 'undefined') {
              ajaxCart.load(false, false, false, true);
            }
          },
          error: function (err) {
            console.error("Error syncing to cart:", err);
          }
        });
      }
    },
    error: function (err) {
      console.error("Error clearing cart:", err);
    }
  });
}

$(document).on("click", ".overlay-buy_button, .result-fixed-btn", function (e) {
  e.preventDefault();

  // Cart is already synced, just proceed to checkout
  window.location.href = '/cart';
});


var USFormatMoney = function (cents) {
    var format = "{{ shop.money_format }}";
    if (typeof cents == "undefined" || cents == null) {
        return ""
    }
    if (typeof cents == "string" && cents.length == 0) {
        return ""
    }

    var value = "",
            placeholderRegex = /\{\{\s*(\w+)\s*\}\}/,
            formatString = format || this.money_format;
    if (typeof cents == "string") {
        cents = cents.replace(".", "")
    }

    function defaultOption(opt, def) {
        return typeof opt == "undefined" ? def : opt
    }

    function formatWithDelimiters(number, precision, thousands, decimal) {
        precision = defaultOption(precision, 2);
        thousands = defaultOption(thousands, ",");
        decimal = defaultOption(decimal, ".");
        if (isNaN(number) || number == null) {
            return 0
        }
        number = (number / 100).toFixed(precision);
        var parts = number.split("."),
                dollars = parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1" + thousands),
                cents = parts[1] ? decimal + parts[1] : "";
        return dollars + cents
    }
    switch (formatString.match(placeholderRegex)[1]) {
        case "amount":
            value = formatWithDelimiters(cents, 2);
            break;
        case "amount_no_decimals":
            value = formatWithDelimiters(cents, 0);
            break;
        case "amount_with_comma_separator":
            value = formatWithDelimiters(cents, 2, ".", ",");
            break;
        case"amount_with_space_separator":
            value = formatWithDelimiters(cents, 2, " ", ",");
            break;
        case"amount_with_period_and_space_separator":
            value = formatWithDelimiters(cents, 2, " ", ".");
            break;
        case "amount_no_decimals_with_comma_separator":
            value = formatWithDelimiters(cents, 0, ".", ",");
            break;
        case"amount_no_decimals_with_space_separator":
            value = formatWithDelimiters(cents, 0, ".", "");
            break;
        case"amount_with_space_separator":
            value = formatWithDelimiters(cents, 2, ",", "");
            break;
        case"amount_with_apostrophe_separator":
            value = formatWithDelimiters(cents, 2, "'", ".")
            break;
    }
    return formatString.replace(placeholderRegex, value)
}
</script>

{% schema %}
{
  "name": "Quiz result banner",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "custom_class",
      "label": "Custom class"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "inline_richtext",
      "id": "suggest_title",
      "label": "Suggest title"
    },
    {
      "type": "product_list",
      "id": "product_list",
      "label": "Select product"
    }
  ],
  "presets": [
    {
      "name": "Quiz result banner"
    }
  ]
}
{% endschema %}
